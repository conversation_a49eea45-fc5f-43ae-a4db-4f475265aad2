<div class="announcement-bar" id="announcement-bar">
  <button class="announcement-bar__arrow announcement-bar__arrow--prev" id="prevBtn" aria-label="Previous announcement">
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  </button>
  
  <div class="announcement-bar__container">
    <div class="announcement-bar__slider" id="announcementSlider">
      <div class="announcement-bar__slide active">
        <span>{{ section.settings.announcement_1 | default: 'Discover the Magic at Wonder Bazaar' }}</span>
      </div>
      <div class="announcement-bar__slide">
        <a href="{{ section.settings.instagram_url | default: 'https://instagram.com/wonder.bazaar' }}" target="_blank" rel="noopener">
          {{ section.settings.announcement_2 | default: 'Join Our Journey on Instagram' }}
        </a>
      </div>
      <div class="announcement-bar__slide">
        <span>{{ section.settings.announcement_3 | default: 'Shop from Anywhere, We Ship Worldwide' }}</span>
      </div>
      <div class="announcement-bar__slide">
        <span>{{ section.settings.announcement_4 | default: 'Enjoy Free Shipping Across India' }}</span>
      </div>
      <div class="announcement-bar__slide">
        <span>{{ section.settings.announcement_5 | default: 'Explore Exclusive Collections Today' }}</span>
      </div>
      <div class="announcement-bar__slide">
        <span>{{ section.settings.announcement_6 | default: 'New Arrivals Just Landed – Check Them Out!' }}</span>
      </div>
    </div>
  </div>
  
  <button class="announcement-bar__arrow announcement-bar__arrow--next" id="nextBtn" aria-label="Next announcement">
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  </button>
</div>

{% stylesheet %}
  .announcement-bar {
    background-color: #000000;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    position: relative;
    overflow: hidden;
    min-height: 3rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .announcement-bar__container {
    flex: 1;
    overflow: hidden;
    margin: 0 0.5rem;
  }

  .announcement-bar__slider {
    display: flex;
    transition: transform 0.3s ease-in-out;
    width: 600%; /* 6 slides * 100% */
  }

  .announcement-bar__slide {
    width: 16.666%; /* 100% / 6 slides */
    flex-shrink: 0;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .announcement-bar__slide a {
    color: inherit;
    text-decoration: none;
    transition: opacity 0.2s ease;
  }

  .announcement-bar__slide a:hover {
    opacity: 0.8;
    text-decoration: underline;
  }

  .announcement-bar__arrow {
    background: none;
    border: none;
    color: #ffffff;
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    flex-shrink: 0;
  }

  .announcement-bar__arrow:hover {
    opacity: 0.7;
    background-color: rgba(255, 255, 255, 0.1);
  }

  .announcement-bar__arrow:active {
    transform: scale(0.95);
  }

  .announcement-bar__arrow svg {
    width: 1rem;
    height: 1rem;
  }

  /* Mobile-first responsive design */
  @media (max-width: 768px) {
    .announcement-bar {
      padding: 0.5rem 0.75rem;
      font-size: 0.8rem;
      min-height: 2.5rem;
    }
    
    .announcement-bar__container {
      margin: 0 0.25rem;
    }
    
    .announcement-bar__arrow {
      width: 1.75rem;
      height: 1.75rem;
      padding: 0.375rem;
    }
    
    .announcement-bar__arrow svg {
      width: 0.875rem;
      height: 0.875rem;
    }
  }

  /* Touch-friendly swipe area */
  .announcement-bar__container {
    touch-action: pan-x;
    user-select: none;
  }
{% endstylesheet %}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('announcementSlider');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const container = slider.parentElement;

    let currentSlide = 0;
    const totalSlides = 6;
    let autoSlideInterval;
    let isUserInteracting = false;

    // Touch/swipe variables
    let startX = 0;
    let currentX = 0;
    let isDragging = false;
    let startTransform = 0;

    function updateSlider() {
      const translateX = -(currentSlide * (100 / totalSlides));
      slider.style.transform = `translateX(${translateX}%)`;
    }

    function nextSlide() {
      currentSlide = (currentSlide + 1) % totalSlides;
      updateSlider();
    }

    function prevSlide() {
      currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
      updateSlider();
    }

    function startAutoSlide() {
      if (autoSlideInterval) clearInterval(autoSlideInterval);
      autoSlideInterval = setInterval(() => {
        if (!isUserInteracting) {
          nextSlide();
        }
      }, 3000);
    }

    function stopAutoSlide() {
      if (autoSlideInterval) {
        clearInterval(autoSlideInterval);
        autoSlideInterval = null;
      }
    }

    // Button event listeners
    nextBtn.addEventListener('click', () => {
      isUserInteracting = true;
      nextSlide();
      setTimeout(() => { isUserInteracting = false; }, 1000);
    });

    prevBtn.addEventListener('click', () => {
      isUserInteracting = true;
      prevSlide();
      setTimeout(() => { isUserInteracting = false; }, 1000);
    });

    // Touch/swipe functionality
    function getEventX(e) {
      return e.type.includes('mouse') ? e.clientX : e.touches[0].clientX;
    }

    function handleStart(e) {
      isDragging = true;
      startX = getEventX(e);
      currentX = startX;
      startTransform = -(currentSlide * (100 / totalSlides));
      isUserInteracting = true;
      container.style.cursor = 'grabbing';
    }

    function handleMove(e) {
      if (!isDragging) return;

      e.preventDefault();
      currentX = getEventX(e);
      const deltaX = currentX - startX;
      const containerWidth = container.offsetWidth;
      const movePercent = (deltaX / containerWidth) * (100 / totalSlides);

      slider.style.transform = `translateX(${startTransform + movePercent}%)`;
    }

    function handleEnd(e) {
      if (!isDragging) return;

      isDragging = false;
      container.style.cursor = 'grab';

      const deltaX = currentX - startX;
      const containerWidth = container.offsetWidth;
      const threshold = containerWidth * 0.2; // 20% of container width

      if (Math.abs(deltaX) > threshold) {
        if (deltaX > 0) {
          prevSlide();
        } else {
          nextSlide();
        }
      } else {
        updateSlider(); // Snap back to current slide
      }

      setTimeout(() => { isUserInteracting = false; }, 1000);
    }

    // Mouse events
    container.addEventListener('mousedown', handleStart);
    document.addEventListener('mousemove', handleMove);
    document.addEventListener('mouseup', handleEnd);

    // Touch events
    container.addEventListener('touchstart', handleStart, { passive: false });
    container.addEventListener('touchmove', handleMove, { passive: false });
    container.addEventListener('touchend', handleEnd);

    // Prevent context menu on long press
    container.addEventListener('contextmenu', e => e.preventDefault());

    // Pause auto-slide on hover/focus
    container.addEventListener('mouseenter', stopAutoSlide);
    container.addEventListener('mouseleave', startAutoSlide);
    container.addEventListener('focusin', stopAutoSlide);
    container.addEventListener('focusout', startAutoSlide);

    // Initialize
    container.style.cursor = 'grab';
    startAutoSlide();
    updateSlider();
  });
</script>

{% schema %}
{
  "name": "Announcement Bar",
  "tag": "section",
  "class": "announcement-bar-section",
  "settings": [
    {
      "type": "text",
      "id": "announcement_1",
      "label": "Announcement 1",
      "default": "Discover the Magic at Wonder Bazaar"
    },
    {
      "type": "text",
      "id": "announcement_2",
      "label": "Announcement 2 (Instagram)",
      "default": "Join Our Journey on Instagram"
    },
    {
      "type": "text",
      "id": "instagram_url",
      "label": "Instagram URL",
      "default": "https://instagram.com/wonder.bazaar"
    },
    {
      "type": "text",
      "id": "announcement_3",
      "label": "Announcement 3",
      "default": "Shop from Anywhere, We Ship Worldwide"
    },
    {
      "type": "text",
      "id": "announcement_4",
      "label": "Announcement 4",
      "default": "Enjoy Free Shipping Across India"
    },
    {
      "type": "text",
      "id": "announcement_5",
      "label": "Announcement 5",
      "default": "Explore Exclusive Collections Today"
    },
    {
      "type": "text",
      "id": "announcement_6",
      "label": "Announcement 6",
      "default": "New Arrivals Just Landed – Check Them Out!"
    }
  ],
  "presets": [
    {
      "name": "Announcement Bar"
    }
  ]
}
{% endschema %}
